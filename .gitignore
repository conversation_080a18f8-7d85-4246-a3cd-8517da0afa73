# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*
.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# testing
__tests__/
jest.config.js
jest.setup.js
coverage/
*.test.js
*.test.ts
*.test.tsx
*.spec.js
*.spec.ts
*.spec.tsx

# SEO and analysis files
lighthouse-report*
lighthouse_report*
lighthouse.*
seo-audit*
seo_audit*
seo-report*
seo_report*
performance-audit*
performance_audit*
performance-report*
performance_report*
accessibility-audit*
accessibility_audit*
accessibility-report*
accessibility_report*
bundle-analyzer-report*
bundle_analyzer_report*
pagespeed-insights*
pagespeed_insights*
web-vitals-report*
web_vitals_report*
core-web-vitals*
core_web_vitals*
gtmetrix-report*
gtmetrix_report*
webpagetest-report*
webpagetest_report*
sitemap-audit*
sitemap_audit*
robots-audit*
robots_audit*
schema-audit*
schema_audit*
meta-audit*
meta_audit*
crawl-report*
crawl_report*
broken-links*
broken_links*
site-audit*
site_audit*
technical-seo*
technical_seo*
on-page-seo*
on_page_seo*
off-page-seo*
off_page_seo*
keyword-analysis*
keyword_analysis*
competitor-analysis*
competitor_analysis*
backlink-analysis*
backlink_analysis*
search-console*
search_console*
analytics-report*
analytics_report*
conversion-report*
conversion_report*
user-experience*
user_experience*
mobile-usability*
mobile_usability*
page-experience*
page_experience*
*.lighthouse
*.pagespeed
*.gtmetrix
*.webpagetest
*.seoreport
*.seoaudit
