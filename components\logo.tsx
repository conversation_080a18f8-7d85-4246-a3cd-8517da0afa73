export function Logo(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="160"
      height="40"
      viewBox="0 0 160 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect width="160" height="40" rx="8" fill="#0A0A0A" />
      <path
        d="M17.312 29V11.8H27.52V14.968H21.056V18.904H26.864V22.072H21.056V29H17.312ZM30.4526 29V15.4H34.1006V29H30.4526ZM32.2846 13.952C31.6926 13.952 31.1886 13.752 30.7726 13.352C30.3726 12.936 30.1726 12.44 30.1726 11.864C30.1726 11.288 30.3726 10.8 30.7726 10.4C31.1886 9.984 31.6926 9.776 32.2846 9.776C32.8766 9.776 33.3726 9.984 33.7726 10.4C34.1886 10.8 34.3966 11.288 34.3966 11.864C34.3966 12.44 34.1886 12.936 33.7726 13.352C33.3726 13.752 32.8766 13.952 32.2846 13.952ZM37.0672 29V15.4H40.5232V17.144H40.6992C40.9312 16.552 41.3312 16.072 41.8992 15.704C42.4672 15.336 43.1312 15.152 43.8912 15.152C44.6672 15.152 45.3472 15.344 45.9312 15.728C46.5152 16.112 46.9232 16.64 47.1552 17.312H47.3152C47.5952 16.656 48.0432 16.128 48.6592 15.728C49.2912 15.328 50.0432 15.128 50.9152 15.128C52.0512 15.128 52.9792 15.496 53.6992 16.232C54.4192 16.952 54.7792 18.024 54.7792 19.448V29H51.1312V20.296C51.1312 19.56 50.9472 19.024 50.5792 18.688C50.2272 18.352 49.7792 18.184 49.2352 18.184C48.6112 18.184 48.1152 18.384 47.7472 18.784C47.3792 19.168 47.1952 19.688 47.1952 20.344V29H43.6192V20.2C43.6192 19.544 43.4432 19.04 43.0912 18.688C42.7552 18.336 42.3152 18.16 41.7712 18.16C41.4032 18.16 41.0752 18.256 40.7872 18.448C40.5152 18.624 40.3072 18.88 40.1632 19.216C40.0192 19.536 39.9472 19.912 39.9472 20.344V29H37.0672ZM62.7833 29.24C61.5833 29.24 60.5193 28.968 59.5913 28.424C58.6633 27.864 57.9353 27.064 57.4073 26.024C56.8953 24.984 56.6393 23.736 56.6393 22.28C56.6393 20.856 56.9033 19.624 57.4313 18.584C57.9593 17.528 58.6873 16.712 59.6153 16.136C60.5433 15.56 61.5993 15.272 62.7833 15.272C63.9033 15.272 64.8873 15.52 65.7353 16.016C66.5993 16.512 67.2793 17.224 67.7753 18.152C68.2713 19.08 68.5193 20.184 68.5193 21.464V22.424H58.3993V20.224H65.1593C65.1593 19.68 65.0473 19.2 64.8233 18.784C64.5993 18.368 64.2873 18.04 63.8873 17.8C63.5033 17.544 63.0553 17.416 62.5433 17.416C62.0153 17.416 61.5433 17.552 61.1273 17.824C60.7273 18.08 60.4153 18.424 60.1913 18.856C59.9673 19.272 59.8553 19.744 59.8553 20.272V23.768C59.8553 24.44 59.9673 25.024 60.1913 25.52C60.4313 26.016 60.7673 26.4 61.1993 26.672C61.6313 26.944 62.1433 27.08 62.7353 27.08C63.1353 27.08 63.4953 27.024 63.8153 26.912C64.1353 26.8 64.4073 26.632 64.6313 26.408C64.8553 26.184 65.0233 25.912 65.1353 25.592L68.4233 25.808C68.2633 26.672 67.9033 27.432 67.3433 28.088C66.7993 28.728 66.0873 29.232 65.2073 29.6C64.3273 29.968 63.3193 29.24 62.7833 29.24Z"
        fill="white"
      />
      <path
        d="M74.208 29V11.8H84.416V14.968H77.952V18.904H83.76V22.072H77.952V29H74.208ZM87.3486 29V15.4H90.9966V29H87.3486ZM89.1806 13.952C88.5886 13.952 88.0846 13.752 87.6686 13.352C87.2686 12.936 87.0686 12.44 87.0686 11.864C87.0686 11.288 87.2686 10.8 87.6686 10.4C88.0846 9.984 88.5886 9.776 89.1806 9.776C89.7726 9.776 90.2686 9.984 90.6686 10.4C91.0846 10.8 91.2926 11.288 91.2926 11.864C91.2926 12.44 91.0846 12.936 90.6686 13.352C90.2686 13.752 89.7726 13.952 89.1806 13.952Z"
        fill="white"
      />
    </svg>
  )
}

